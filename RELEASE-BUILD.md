# LinkEye Release构建指南

## 概述

本项目已配置完整的Release版本构建流程，包括代码签名、混淆优化和版本管理。

## 文件结构

```
├── app/
│   ├── release-key.keystore          # Release签名密钥库
│   ├── version.properties            # 版本配置文件
│   ├── build.gradle                  # 构建配置（已配置签名和混淆）
│   └── proguard-rules.pro           # ProGuard混淆规则
├── build-release.bat                 # Release构建脚本
├── update-version.bat                # 版本更新脚本
└── RELEASE-BUILD.md                  # 本文档
```

## 签名配置

### 密钥库信息
- **文件**: `app/release-key.keystore`
- **别名**: `linkeye-release`
- **密码**: `linkeye123`
- **有效期**: 2053年1月12日

### 证书信息
- **CN**: LinkEye
- **OU**: Development
- **O**: LinkEye Company
- **L**: Beijing
- **ST**: Beijing
- **C**: CN

## 构建流程

### 1. 快速构建Release APK

```bash
# 运行构建脚本
build-release.bat
```

### 2. 手动构建

```bash
# 清理之前的构建
./gradlew clean

# 构建Release APK
./gradlew assembleRelease
```

### 3. 更新版本

```bash
# 更新版本号
update-version.bat 1.1.0 2

# 然后构建新版本
build-release.bat
```

## 版本管理

版本信息存储在 `app/version.properties` 文件中：

```properties
VERSION_CODE=1
VERSION_NAME=1.0.0
```

- `VERSION_CODE`: 整数，每次发布递增
- `VERSION_NAME`: 语义化版本号（如 1.0.0）

## Release配置特性

### 代码混淆和优化
- ✅ 启用ProGuard代码混淆
- ✅ 启用资源压缩
- ✅ 移除调试信息
- ✅ 优化代码大小

### 构建信息
每个Release版本包含以下构建信息：
- 构建时间
- Git提交哈希（如果可用）
- 版本号和版本名

### 签名验证
构建完成后可以验证APK签名：

```bash
jarsigner -verify -verbose -certs app/build/outputs/apk/release/app-release.apk
```

## 输出文件

Release APK位置：
```
app/build/outputs/apk/release/app-release.apk
```

## 注意事项

1. **密钥库安全**: 请妥善保管 `release-key.keystore` 文件和密码
2. **版本管理**: 每次发布前记得更新版本号
3. **测试**: Release版本发布前请充分测试
4. **备份**: 建议备份密钥库文件到安全位置

## 故障排除

### 构建失败
1. 检查Java和Android SDK环境
2. 确保密钥库文件存在且密码正确
3. 检查ProGuard规则是否有冲突

### 签名验证失败
1. 确认密钥库文件完整
2. 检查签名配置是否正确
3. 验证密钥库密码

## 发布检查清单

- [ ] 更新版本号
- [ ] 测试Debug版本功能
- [ ] 构建Release APK
- [ ] 验证APK签名
- [ ] 测试Release APK功能
- [ ] 检查APK大小
- [ ] 备份构建产物
