<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:id="@+id/tile_root"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:foregroundGravity="center"
	android:focusable="true"
	android:focusableInTouchMode="false"
	android:clickable="true"
	android:longClickable="true">

	<SurfaceView
		android:id="@+id/surface_view"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:layout_gravity="center" />

	<LinearLayout
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:layout_gravity="top|end"
		android:orientation="horizontal"
		android:layout_margin="8dp">
		<TextView
			android:id="@+id/title"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:textColor="#FFFFFF"
			android:textSize="14sp"
			android:text="Cam"
			android:background="@drawable/title_background"
			android:paddingStart="8dp"
			android:paddingEnd="8dp"
			android:paddingTop="4dp"
			android:paddingBottom="4dp"
			android:layout_marginEnd="8dp" />
		<ImageView
			android:id="@+id/mute_badge"
			android:layout_width="24dp"
			android:layout_height="24dp"
			android:src="@drawable/ic_mute"
			android:background="@drawable/mute_badge_background"
			android:padding="4dp"
			android:visibility="gone"/>
	</LinearLayout>

	<View
		android:id="@+id/focus_border"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:background="@android:color/transparent"/>

	<ProgressBar
		android:id="@+id/loading_indicator"
		android:layout_width="32dp"
		android:layout_height="32dp"
		android:layout_gravity="center"
		android:visibility="gone"
		style="?android:attr/progressBarStyle" />

</FrameLayout>
