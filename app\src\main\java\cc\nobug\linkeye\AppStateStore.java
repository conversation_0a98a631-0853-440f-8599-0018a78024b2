package cc.nobug.linkeye;

import android.content.Context;
import android.content.SharedPreferences;

/**
 * 应用状态存储：负责网格模式、页码、电视模式等基础状态的读写
 * 封装 SharedPreferences，统一键名与默认值
 */
public final class AppStateStore {

	// 偏好存储文件名与键名（与现有 MainActivity 保持一致以保障兼容）
	public static final String PREFS = "tv_prefs";
	public static final String KEY_GRID = "grid"; // 1/2/3/4 或 特殊 TV
	public static final String KEY_PAGE = "page";
	public static final String KEY_TV_MODE = "tv_mode"; // 是否电视模式

	private final SharedPreferences sharedPreferences;

	public AppStateStore(Context context) {
		this.sharedPreferences = context.getSharedPreferences(PREFS, Context.MODE_PRIVATE);
	}

	/** 保存基础状态 */
	public void saveBasicState(int gridMode, boolean isTvMode, int page) {
		SharedPreferences.Editor ed = sharedPreferences.edit();
		ed.putInt(KEY_GRID, isTvMode ? 4 : gridMode);
		ed.putBoolean(KEY_TV_MODE, isTvMode);
		ed.putInt(KEY_PAGE, page);
		ed.apply();
	}

	public int getGridMode() {
		return sharedPreferences.getInt(KEY_GRID, 4);
	}

	public boolean isTvMode() {
		return sharedPreferences.getBoolean(KEY_TV_MODE, false);
	}

	public int getPage() {
		return sharedPreferences.getInt(KEY_PAGE, 0);
	}
}


