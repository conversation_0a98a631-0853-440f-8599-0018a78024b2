package cc.nobug.linkeye;

import java.util.ArrayList;
import java.util.List;

/**
 * 频道切换器：提供上一/下一频道查找与冷却判断
 */
public final class ChannelSwitcher {

	private ChannelSwitcher() { }

	/** 冷却期内禁止切换 */
	public static boolean isWithinCooldown(long lastSwitchTimeMs, long nowMs, long cooldownMs) {
		return (nowMs - lastSwitchTimeMs) < cooldownMs;
	}

	/** 拉平成一维频道列表 */
	private static List<TvChannel> flattenChannels(List<TvGroup> groups) {
		List<TvChannel> all = new ArrayList<>();
		if (groups == null) return all;
		for (TvGroup g : groups) {
			if (g != null && g.channels != null) all.addAll(g.channels);
		}
		return all;
	}

	/** 获取下一频道（环绕） */
	public static TvChannel findNext(List<TvGroup> groups, CameraInfo current) {
		List<TvChannel> all = flattenChannels(groups);
		if (all.isEmpty()) return null;
		if (!(current instanceof TvChannel)) return all.get(0);
		TvChannel cur = (TvChannel) current;
		int idx = indexOf(all, cur);
		if (idx >= 0) {
			int next = (idx + 1) % all.size();
			return all.get(next);
		}
		return all.get(0);
	}

	/** 获取上一频道（环绕） */
	public static TvChannel findPrevious(List<TvGroup> groups, CameraInfo current) {
		List<TvChannel> all = flattenChannels(groups);
		if (all.isEmpty()) return null;
		if (!(current instanceof TvChannel)) return all.get(all.size() - 1);
		TvChannel cur = (TvChannel) current;
		int idx = indexOf(all, cur);
		if (idx >= 0) {
			int prev = (idx - 1 + all.size()) % all.size();
			return all.get(prev);
		}
		return all.get(all.size() - 1);
	}

	private static int indexOf(List<TvChannel> list, TvChannel target) {
		for (int i = 0; i < list.size(); i++) {
			TvChannel c = list.get(i);
			if (c != null
				&& safeEq(c.name, target.name)
				&& safeEq(c.groupTitle, target.groupTitle)) {
				return i;
			}
		}
		return -1;
	}

	private static boolean safeEq(String a, String b) {
		return a == null ? b == null : a.equals(b);
	}
}


