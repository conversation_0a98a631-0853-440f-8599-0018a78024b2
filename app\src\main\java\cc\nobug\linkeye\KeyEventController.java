package cc.nobug.linkeye;

import android.view.KeyEvent;
import android.view.View;

import java.util.List;

/**
 * 按键事件控制器：集中处理 onKeyDown 逻辑
 */
public final class KeyEventController {

	public interface Callbacks {
		boolean isTvMode();
		int getGridMode();
		List<PlayerTileView> getTiles();
		void chooseGridMode();
		void showTileMenu(int tileIndex);
		void pageNext();
		void pagePrev();
		void switchToNextChannel(int tileIndex);
		void switchToPreviousChannel(int tileIndex);
	}

	private final Callbacks callbacks;

	public KeyEventController(Callbacks callbacks) {
		this.callbacks = callbacks;
	}

	/**
	 * 处理按键；返回 true 表示已消费
	 */
	public boolean onKeyDown(int keyCode, View currentFocus) {
		// MENU 打开设置
		if (keyCode == KeyEvent.KEYCODE_MENU) {
			callbacks.chooseGridMode();
			return true;
		}

		// 确认键打开 tile 菜单
		if (keyCode == KeyEvent.KEYCODE_DPAD_CENTER
				|| keyCode == KeyEvent.KEYCODE_ENTER
				|| keyCode == KeyEvent.KEYCODE_BUTTON_A) {
			if (currentFocus instanceof PlayerTileView) {
				int tileIndex = callbacks.getTiles().indexOf(currentFocus);
				if (tileIndex >= 0) {
					callbacks.showTileMenu(tileIndex);
					return true;
				}
			}
		}

		// 返回键交由系统处理
		if (keyCode == KeyEvent.KEYCODE_BACK || keyCode == KeyEvent.KEYCODE_BUTTON_B) {
			return false;
		}

		// 电视模式：主 tile 用上下切换频道
		if (callbacks.isTvMode()) {
			if (currentFocus instanceof PlayerTileView) {
				int tileIndex = callbacks.getTiles().indexOf(currentFocus);
				if (tileIndex == 0) {
					if (keyCode == KeyEvent.KEYCODE_DPAD_UP) { callbacks.switchToPreviousChannel(tileIndex); return true; }
					if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) { callbacks.switchToNextChannel(tileIndex); return true; }
				}
			}
		}

		// 非电视模式，4 宫格翻页
		if (!callbacks.isTvMode() && callbacks.getGridMode() == 4) {
			if (keyCode == KeyEvent.KEYCODE_CHANNEL_UP || keyCode == KeyEvent.KEYCODE_PAGE_UP || keyCode == KeyEvent.KEYCODE_MEDIA_NEXT) {
				callbacks.pageNext();
				return true;
			}
			if (keyCode == KeyEvent.KEYCODE_CHANNEL_DOWN || keyCode == KeyEvent.KEYCODE_PAGE_DOWN || keyCode == KeyEvent.KEYCODE_MEDIA_PREVIOUS) {
				callbacks.pagePrev();
				return true;
			}
		}

		return false;
	}
}


