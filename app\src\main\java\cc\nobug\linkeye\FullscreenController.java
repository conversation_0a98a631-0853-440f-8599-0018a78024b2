package cc.nobug.linkeye;

import android.app.Activity;
import android.os.Build;
import android.view.View;
import android.view.WindowManager;

/**
 * 全屏控制器：集中管理沉浸式全屏相关逻辑
 * 目的：从 MainActivity 中抽离窗口外观控制，减少职责耦合
 */
public final class FullscreenController {

	private FullscreenController() { }

	/**
	 * 启用沉浸式全屏，并保持屏幕常亮
	 */
	public static void enableFullscreen(Activity activity) {
		if (activity == null) return;
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
			activity.getWindow().getDecorView().setSystemUiVisibility(
				View.SYSTEM_UI_FLAG_LAYOUT_STABLE
				| View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
				| View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
				| View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
				| View.SYSTEM_UI_FLAG_FULLSCREEN
				| View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
			);
		}

		// 屏幕常亮
		activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

		// 隐藏 ActionBar（若存在）
		if (activity.getActionBar() != null) {
			activity.getActionBar().hide();
		}
	}

	/**
	 * 窗口焦点变化时，重新应用全屏参数
	 */
	public static void onWindowFocusChanged(Activity activity, boolean hasFocus) {
		if (hasFocus) {
			enableFullscreen(activity);
		}
	}
}


