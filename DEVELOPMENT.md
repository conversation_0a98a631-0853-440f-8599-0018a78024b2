# LinkEye 开发指南

## 首次设置

### 1. 克隆项目后的配置
```bash
# 复制签名配置模板
cp keystore.properties.example keystore.properties

# 编辑keystore.properties，填入你的签名信息
# 注意：keystore.properties不会提交到git
```

### 2. 签名文件配置
- 将你的keystore文件放在app目录下
- 在keystore.properties中配置正确的路径和密码
- 确保keystore文件不会被提交到版本控制

## 开发流程

### 版本管理
使用 `update-version.bat` 脚本更新版本：
```bash
# 更新版本号
update-version.bat
```

### 构建流程
```bash
# Debug构建（开发测试）
./gradlew assembleDebug

# Release构建（发布）
./gradlew assembleRelease
# 或使用
build-release.bat
```

### 代码规范
- 遵循Kotlin官方代码风格
- 使用有意义的变量和函数名
- 添加必要的注释

## 安全注意事项

### 敏感信息处理
- ❌ 不要将密码、API密钥等敏感信息直接写在代码中
- ❌ 不要提交keystore文件到版本控制
- ✅ 使用keystore.properties存储签名信息
- ✅ 使用BuildConfig存储构建时信息

### 文件权限
确保以下文件不会被提交：
- `local.properties` - 包含本地SDK路径
- `keystore.properties` - 包含签名密码
- `*.keystore`, `*.jks` - 签名文件
- `build/` - 构建输出目录

## 构建优化

### Gradle优化
项目已配置以下优化选项：
- 并行构建：`org.gradle.parallel=true`
- 构建缓存：`org.gradle.caching=true`
- 按需配置：`org.gradle.configureondemand=true`
- Android构建缓存：`android.enableBuildCache=true`

### APK优化
Release构建已启用：
- 代码混淆：`minifyEnabled true`
- 资源压缩：`shrinkResources true`
- ZIP对齐：`zipAlignEnabled true`
- ABI分包：支持armeabi-v7a, arm64-v8a, x86, x86_64

## 故障排除

### 常见问题
1. **构建失败**：检查SDK路径和版本
2. **签名错误**：确认keystore.properties配置正确
3. **依赖冲突**：清理构建缓存 `./gradlew clean`

### 清理命令
```bash
# 清理构建缓存
./gradlew clean

# 清理Gradle缓存
./gradlew cleanBuildCache
```
