package cc.nobug.linkeye;

import android.view.View;
import java.util.List;

/**
 * 焦点导航器：封装不同布局下的焦点连线逻辑
 */
public final class FocusNavigator {

	private FocusNavigator() { }

	/** 普通网格（1/2/4）下的焦点设置 */
	public static void setupNormal(List<PlayerTileView> tiles, int gridMode) {
		if (tiles == null || tiles.isEmpty()) return;
		if (gridMode == 3) { return; }
		int columns = (gridMode == 1) ? 1 : 2;
		int rows = (gridMode == 1) ? 1 : (gridMode == 2) ? 1 : 2;
		for (int i = 0; i < tiles.size(); i++) {
			PlayerTileView tile = tiles.get(i);
			int row = i / columns;
			int col = i % columns;
			tile.setNextFocusUpId(View.NO_ID);
			tile.setNextFocusDownId(View.NO_ID);
			tile.setNextFocusLeftId(View.NO_ID);
			tile.setNextFocusRightId(View.NO_ID);
			int upIndex = ((row - 1 + rows) % rows) * columns + col;
			if (upIndex < tiles.size() && upIndex != i) tile.setNextFocusUpId(tiles.get(upIndex).getId());
			int downIndex = ((row + 1) % rows) * columns + col;
			if (downIndex < tiles.size() && downIndex != i) tile.setNextFocusDownId(tiles.get(downIndex).getId());
			int leftIndex = row * columns + ((col - 1 + columns) % columns);
			if (leftIndex < tiles.size() && leftIndex != i) tile.setNextFocusLeftId(tiles.get(leftIndex).getId());
			int rightIndex = row * columns + ((col + 1) % columns);
			if (rightIndex < tiles.size() && rightIndex != i) tile.setNextFocusRightId(tiles.get(rightIndex).getId());
		}
	}

	/** 3 路布局下的焦点设置：0 主，1 右上，2 右下 */
	public static void setupThreeWay(List<PlayerTileView> tiles) {
		if (tiles == null || tiles.size() != 3) return;
		PlayerTileView mainTile = tiles.get(0);
		PlayerTileView subTile1 = tiles.get(1);
		PlayerTileView subTile2 = tiles.get(2);
		for (PlayerTileView tile : tiles) {
			tile.setNextFocusUpId(View.NO_ID);
			tile.setNextFocusDownId(View.NO_ID);
			tile.setNextFocusLeftId(View.NO_ID);
			tile.setNextFocusRightId(View.NO_ID);
		}
		mainTile.setNextFocusRightId(subTile1.getId());
		subTile1.setNextFocusLeftId(mainTile.getId());
		subTile1.setNextFocusDownId(subTile2.getId());
		subTile2.setNextFocusLeftId(mainTile.getId());
		subTile2.setNextFocusUpId(subTile1.getId());
	}

	/** 电视模式下的焦点设置：0 主，1 右上悬浮，2 右下悬浮 */
	public static void setupTvMode(List<PlayerTileView> tiles) {
		if (tiles == null || tiles.size() != 3) return;
		PlayerTileView mainTile = tiles.get(0);
		PlayerTileView floating1 = tiles.get(1);
		PlayerTileView floating2 = tiles.get(2);
		for (PlayerTileView tile : tiles) {
			tile.setNextFocusUpId(View.NO_ID);
			tile.setNextFocusDownId(View.NO_ID);
			tile.setNextFocusLeftId(View.NO_ID);
			tile.setNextFocusRightId(View.NO_ID);
		}
		mainTile.setNextFocusRightId(floating1.getId());
		floating1.setNextFocusLeftId(mainTile.getId());
		floating1.setNextFocusDownId(floating2.getId());
		floating2.setNextFocusLeftId(mainTile.getId());
		floating2.setNextFocusUpId(floating1.getId());
	}
}


