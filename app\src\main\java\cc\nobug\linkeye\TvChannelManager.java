package cc.nobug.linkeye;

import android.content.Context;

import java.util.List;

/**
 * 电视频道管理器：封装 M3U8 加载、测试与 URL 存取
 */
public final class TvChannelManager {

	private TvChannelManager() { }

	/** 回调接口：返回频道分组或错误信息 */
	public interface ChannelsCallback {
		void onSuccess(List<TvGroup> groups);
		void onError(String error);
	}

	/** 异步加载频道列表 */
	public static void loadChannels(Context context, ChannelsCallback callback) {
		M3U8Parser.loadM3U8Async(context, new M3U8Parser.M3U8LoadCallback() {
			@Override public void onSuccess(List<TvGroup> groups) { if (callback != null) callback.onSuccess(groups); }
			@Override public void onError(String error) { if (callback != null) callback.onError(error); }
		});
	}

	/** 测试指定 URL 的连接有效性（实际为尝试加载） */
	public static void testConnection(Context context, String url, ChannelsCallback callback) {
		// 复用 loadM3U8Async 实现
		M3U8Parser.loadM3U8Async(context, new M3U8Parser.M3U8LoadCallback() {
			@Override public void onSuccess(List<TvGroup> groups) { if (callback != null) callback.onSuccess(groups); }
			@Override public void onError(String error) { if (callback != null) callback.onError(error); }
		});
	}

	public static String getM3U8Url(Context context) {
		return M3U8Parser.getM3U8Url(context);
	}

	public static void setM3U8Url(Context context, String url) {
		M3U8Parser.setM3U8Url(context, url);
	}
}


