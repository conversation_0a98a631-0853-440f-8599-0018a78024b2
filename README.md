# LinkEye Android App

## 项目配置

### 环境要求
- Android Studio Arctic Fox 或更高版本
- JDK 8 或更高版本
- Android SDK API 35
- NDK 27.0.12077973

### 构建配置

#### 签名配置
为了安全起见，签名信息不应直接写在build.gradle中。请按以下步骤配置：

1. 复制 `keystore.properties.example` 为 `keystore.properties`
2. 在 `keystore.properties` 中填入实际的签名信息：
   ```
   storePassword=你的keystore密码
   keyPassword=你的key密码
   keyAlias=你的key别名
   storeFile=你的keystore文件路径
   ```

#### 版本管理
版本信息在 `app/version.properties` 中管理：
- `VERSION_CODE`: 版本号（整数）
- `VERSION_NAME`: 版本名称（字符串）

使用 `update-version.bat` 脚本可以自动更新版本号。

### 构建命令

#### Debug构建
```bash
./gradlew assembleDebug
```

#### Release构建
```bash
./gradlew assembleRelease
```
或使用批处理脚本：
```bash
build-release.bat
```

### 项目结构
```
link-eye/
├── app/                    # 主应用模块
│   ├── src/               # 源代码
│   ├── build.gradle       # 应用级构建配置
│   ├── version.properties # 版本配置
│   └── release-key.keystore # 签名文件（不提交到git）
├── build.gradle           # 项目级构建配置
├── gradle.properties      # Gradle配置
├── local.properties       # 本地SDK路径（不提交到git）
└── .gitignore            # Git忽略文件配置
```

### 注意事项
- `local.properties` 和 `keystore.properties` 不会提交到版本控制
- 签名文件 `*.keystore` 和 `*.jks` 已配置为忽略
- 构建输出目录 `build/` 已忽略
- IDE配置文件已适当忽略

### 开发建议
1. 使用debug构建进行日常开发
2. 发布前使用release构建进行测试
3. 定期更新依赖版本
4. 遵循Android开发最佳实践
