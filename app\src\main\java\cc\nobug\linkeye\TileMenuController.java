package cc.nobug.linkeye;

import android.app.AlertDialog;
import android.content.Context;
import android.widget.Toast;

import java.util.ArrayList;
import java.util.List;

/**
 * 菜单控制器：负责长按菜单、摄像头/频道选择、静音开关与 M3U8 设置
 */
public class TileMenuController {

	private final Context context;
	private final List<PlayerTileView> tiles;
	private final List<CameraInfo> cameras;
	private final List<TvGroup> tvGroups;
	private final TileAssignmentController.SaveCallback saveCallback;
	private final TvModeProvider tvModeProvider;

	private AlertDialog currentMenuDialog = null;

	public TileMenuController(Context context,
			List<PlayerTileView> tiles,
			List<CameraInfo> cameras,
			List<TvGroup> tvGroups,
			TileAssignmentController.SaveCallback saveCallback,
			TvModeProvider tvModeProvider) {
		this.context = context;
		this.tiles = tiles;
		this.cameras = cameras;
		this.tvGroups = tvGroups;
		this.saveCallback = saveCallback;
		this.tvModeProvider = tvModeProvider;
	}

	/** 显示长按菜单 */
	public void showTileMenu(int tileIndex) {
		if (currentMenuDialog != null && currentMenuDialog.isShowing()) {
			currentMenuDialog.dismiss();
			currentMenuDialog = null;
		}
		if (isTvModeMainTile(tileIndex)) {
			showTvChannelPicker(tileIndex);
			return;
		}
		String[] items = new String[]{"选择摄像头", "静音开关"};
		currentMenuDialog = new AlertDialog.Builder(context)
			.setTitle("设置")
			.setItems(items, (dialog, which) -> {
				if (which == 0) {
					dialog.dismiss();
					currentMenuDialog = null;
					// 小延迟，避免焦点与对话框关闭冲突
					getGrid().postDelayed(() -> showCameraPicker(tileIndex), 150);
				} else if (which == 1) {
					dialog.dismiss();
					currentMenuDialog = null;
					toggleMute(tileIndex);
				}
			})
			.setOnDismissListener(d -> currentMenuDialog = null)
			.create();
		currentMenuDialog.show();
	}

	/** 显示摄像头选择 */
	public void showCameraPicker(int tileIndex) {
		if (currentMenuDialog != null && currentMenuDialog.isShowing()) {
			currentMenuDialog.dismiss();
			currentMenuDialog = null;
		}
		List<String> names = new ArrayList<>();
		names.add("置空");
		for (CameraInfo c : cameras) names.add(c.name);
		AlertDialog cameraDialog = new AlertDialog.Builder(context)
			.setTitle("选择摄像头")
			.setItems(names.toArray(new String[0]), (dialog, which) -> {
				dialog.dismiss();
				if (which == 0) {
					TileAssignmentController.setTileCamera(tiles, cameras, tileIndex, -1, saveCallback);
				} else {
					int camIndex = which - 1;
					TileAssignmentController.setTileCamera(tiles, cameras, tileIndex, camIndex, saveCallback);
				}
			})
			.create();
		cameraDialog.show();
	}

	/** 显示电视分组选择 */
	public void showTvChannelPicker(int tileIndex) {
		if (currentMenuDialog != null && currentMenuDialog.isShowing()) {
			currentMenuDialog.dismiss();
			currentMenuDialog = null;
		}
		if (tvGroups.isEmpty()) {
			Toast.makeText(context, "没有可用的电视频道", Toast.LENGTH_SHORT).show();
			return;
		}
		List<String> groupNames = new ArrayList<>();
		for (TvGroup group : tvGroups) groupNames.add(group.name + " (" + group.getChannelCount() + "个频道)");
		AlertDialog groupDialog = new AlertDialog.Builder(context)
			.setTitle("选择频道分组")
			.setItems(groupNames.toArray(new String[0]), (dialog, which) -> {
				dialog.dismiss();
				if (which >= 0 && which < tvGroups.size()) {
					showChannelsInGroup(tileIndex, tvGroups.get(which));
				}
			})
			.create();
		groupDialog.show();
	}

	/** 显示分组内频道列表 */
	public void showChannelsInGroup(int tileIndex, TvGroup group) {
		if (group == null || group.isEmpty()) {
			Toast.makeText(context, "该分组没有频道", Toast.LENGTH_SHORT).show();
			return;
		}
		List<String> channelNames = new ArrayList<>();
		channelNames.add("置空");
		for (TvChannel channel : group.channels) {
			String name = channel.name;
			if (channel.getSourceCount() > 1) name += " (" + channel.getSourceCount() + "个源)";
			channelNames.add(name);
		}
		AlertDialog channelDialog = new AlertDialog.Builder(context)
			.setTitle(group.name + " - 选择频道")
			.setItems(channelNames.toArray(new String[0]), (dialog, which) -> {
				dialog.dismiss();
				if (which == 0) {
					TileAssignmentController.setTileCamera(tiles, cameras, tileIndex, -1, saveCallback);
				} else {
					int channelIndex = which - 1;
					if (channelIndex >= 0 && channelIndex < group.channels.size()) {
						TvChannel selectedChannel = group.channels.get(channelIndex);
						TileAssignmentController.setTileTvChannel(tiles, tileIndex, selectedChannel, saveCallback);
					}
				}
			})
			.create();
		channelDialog.show();
	}

	/** 静音开关并保存 */
	public void toggleMute(int tileIndex) {
		if (tileIndex < 0 || tileIndex >= tiles.size()) return;
		PlayerTileView tile = tiles.get(tileIndex);
		boolean newMutedState = !tile.isMuted();
		tile.setMuted(newMutedState);
		CameraInfo camera = tile.getAssignedCamera();
		String cameraId = camera != null ? camera.id : "";
		if (saveCallback != null) saveCallback.save(tileIndex, cameraId, newMutedState);
		tile.post(tile::requestFocus);
	}

	/** M3U8 URL 设置与连接测试 */
	public void showM3U8UrlDialog() {
		android.widget.EditText editText = new android.widget.EditText(context);
		editText.setText(TvChannelManager.getM3U8Url(context));
		editText.setHint("输入M3U8播放列表URL");
		new AlertDialog.Builder(context)
			.setTitle("设置M3U8地址")
			.setMessage("请输入M3U8播放列表的网络地址：")
			.setView(editText)
			.setPositiveButton("确定", (dialog, which) -> {
				String url = editText.getText().toString().trim();
				if (!url.isEmpty()) {
					TvChannelManager.setM3U8Url(context, url);
					Toast.makeText(context, "M3U8地址已保存，正在重新加载频道...", Toast.LENGTH_SHORT).show();
					TvChannelManager.loadChannels(context, new TvChannelManager.ChannelsCallback() {
						@Override public void onSuccess(List<TvGroup> groups) { tvGroups.clear(); tvGroups.addAll(groups); }
						@Override public void onError(String error) { Toast.makeText(context, "加载电视频道失败: " + error, Toast.LENGTH_LONG).show(); }
					});
				}
			})
			.setNegativeButton("取消", null)
			.setNeutralButton("测试连接", (dialog, which) -> {
				String url = editText.getText().toString().trim();
				if (!url.isEmpty()) testM3U8Connection(url);
			})
			.show();
	}

	private void testM3U8Connection(String url) {
		Toast.makeText(context, "正在测试连接...", Toast.LENGTH_SHORT).show();
		TvChannelManager.testConnection(context, url, new TvChannelManager.ChannelsCallback() {
			@Override
			public void onSuccess(List<TvGroup> groups) {
				int totalChannels = 0;
				for (TvGroup group : groups) totalChannels += group.getChannelCount();
				Toast.makeText(context, "连接成功！找到 " + groups.size() + " 个分组，共 " + totalChannels + " 个频道", Toast.LENGTH_LONG).show();
			}
			@Override
			public void onError(String error) {
				Toast.makeText(context, "连接失败: " + error, Toast.LENGTH_LONG).show();
			}
		});
	}

	private GridLayoutProvider gridProvider;
	private GridLayoutProvider getGrid() {
		if (gridProvider == null && context instanceof GridLayoutProvider) {
			gridProvider = (GridLayoutProvider) context;
		}
		return gridProvider;
	}

	/**
	 * 提供 GridLayout，用于在菜单关闭后延迟回调（避免焦点抖动）
	 * 由 Activity 实现该接口
	 */
	public interface GridLayoutProvider {
		android.widget.GridLayout getGridLayout();
		default void postDelayed(Runnable r, long delayMs) { getGridLayout().postDelayed(r, delayMs); }
	}

	private boolean isTvModeMainTile(int tileIndex) {
		// 当处于电视模式且索引为 0 时，菜单直接进入频道选择
		return tvModeProvider != null && tvModeProvider.isTvMode() && tileIndex == 0;
	}

	public interface TvModeProvider {
		boolean isTvMode();
	}
}


