package cc.nobug.linkeye;

import android.content.Context;
import android.widget.GridLayout;
import android.view.View;

import java.util.List;

/**
 * 网格控制器：负责构建不同布局（普通/三分屏/电视模式）
 */
public final class TileGridController {

	private TileGridController() { }

	/** 构建普通 1/2/4 网格 */
	public static void buildNormalGrid(Context context, GridLayout grid, List<PlayerTileView> tiles, int gridMode, TileLongClickListener listener) {
		if (gridMode == 3) { build3WayGrid(context, grid, tiles, listener); return; }
		int columns;
		int rows;
		if (gridMode == 1) { columns = 1; rows = 1; }
		else if (gridMode == 2) { columns = 2; rows = 1; }
		else { columns = 2; rows = 2; }
		grid.setColumnCount(columns);
		grid.setRowCount(rows);
		int tileCount = gridMode;
		for (int i = 0; i < tileCount; i++) {
			PlayerTileView tile = new PlayerTileView(context);
			tile.setId(View.generateViewId());
			GridLayout.LayoutParams lp = new GridLayout.LayoutParams();
			lp.width = 0;
			lp.height = 0;
			lp.columnSpec = GridLayout.spec(i % columns, 1f);
			lp.rowSpec = GridLayout.spec(i / columns, 1f);
			int margin = 2;
			lp.setMargins(margin, margin, margin, margin);
			tile.setLayoutParams(lp);
			final int idx = i;
			tile.setOnLongClickListener(v -> {
				if (listener != null) listener.onTileLongClick(idx);
				return true;
			});
			tiles.add(tile);
			grid.addView(tile);
		}
	}

	/** 构建三分屏布局 */
	public static void build3WayGrid(Context context, GridLayout grid, List<PlayerTileView> tiles, TileLongClickListener listener) {
		grid.setColumnCount(5);
		grid.setRowCount(2);
		PlayerTileView mainTile = new PlayerTileView(context);
		mainTile.setId(View.generateViewId());
		GridLayout.LayoutParams mainLp = new GridLayout.LayoutParams();
		mainLp.width = 0;
		mainLp.height = 0;
		mainLp.columnSpec = GridLayout.spec(0, 3, 2f);
		mainLp.rowSpec = GridLayout.spec(0, 2, 1f);
		int margin = 2;
		mainLp.setMargins(margin, margin, margin, margin);
		mainTile.setLayoutParams(mainLp);
		final int mainIdx = 0;
		mainTile.setOnLongClickListener(v -> { if (listener != null) listener.onTileLongClick(mainIdx); return true; });
		tiles.add(mainTile);
		grid.addView(mainTile);

		PlayerTileView subTile1 = new PlayerTileView(context);
		subTile1.setId(View.generateViewId());
		GridLayout.LayoutParams sub1Lp = new GridLayout.LayoutParams();
		sub1Lp.width = 0;
		sub1Lp.height = 0;
		sub1Lp.columnSpec = GridLayout.spec(3, 2, 1f);
		sub1Lp.rowSpec = GridLayout.spec(0, 1f);
		sub1Lp.setMargins(margin, margin, margin, margin);
		subTile1.setLayoutParams(sub1Lp);
		final int sub1Idx = 1;
		subTile1.setOnLongClickListener(v -> { if (listener != null) listener.onTileLongClick(sub1Idx); return true; });
		tiles.add(subTile1);
		grid.addView(subTile1);

		PlayerTileView subTile2 = new PlayerTileView(context);
		subTile2.setId(View.generateViewId());
		GridLayout.LayoutParams sub2Lp = new GridLayout.LayoutParams();
		sub2Lp.width = 0;
		sub2Lp.height = 0;
		sub2Lp.columnSpec = GridLayout.spec(3, 2, 1f);
		sub2Lp.rowSpec = GridLayout.spec(1, 1f);
		sub2Lp.setMargins(margin, margin, margin, margin);
		subTile2.setLayoutParams(sub2Lp);
		final int sub2Idx = 2;
		subTile2.setOnLongClickListener(v -> { if (listener != null) listener.onTileLongClick(sub2Idx); return true; });
		tiles.add(subTile2);
		grid.addView(subTile2);
	}

	/** 构建电视模式布局 */
	public static void buildTvModeGrid(Context context, GridLayout grid, List<PlayerTileView> tiles, TileLongClickListener listener) {
		grid.setColumnCount(3);
		grid.setRowCount(2);
		PlayerTileView mainTile = new PlayerTileView(context);
		mainTile.setId(View.generateViewId());
		GridLayout.LayoutParams mainLp = new GridLayout.LayoutParams();
		mainLp.width = 0;
		mainLp.height = 0;
		mainLp.columnSpec = GridLayout.spec(0, 3, 1f);
		mainLp.rowSpec = GridLayout.spec(0, 2, 1f);
		mainTile.setLayoutParams(mainLp);
		final int mainIdx = 0;
		mainTile.setOnLongClickListener(v -> { if (listener != null) listener.onTileLongClick(mainIdx); return true; });
		tiles.add(mainTile);
		grid.addView(mainTile);

		PlayerTileView floatingTile1 = new PlayerTileView(context);
		floatingTile1.setId(View.generateViewId());
		GridLayout.LayoutParams floating1Lp = new GridLayout.LayoutParams();
		floating1Lp.width = dpToPx(context, 200);
		floating1Lp.height = dpToPx(context, 150);
		floating1Lp.columnSpec = GridLayout.spec(2);
		floating1Lp.rowSpec = GridLayout.spec(0);
		floating1Lp.setGravity(android.view.Gravity.END | android.view.Gravity.TOP);
		floating1Lp.setMargins(8, 8, 8, 4);
		floatingTile1.setLayoutParams(floating1Lp);
		final int floating1Idx = 1;
		floatingTile1.setOnLongClickListener(v -> { if (listener != null) listener.onTileLongClick(floating1Idx); return true; });
		tiles.add(floatingTile1);
		grid.addView(floatingTile1);

		PlayerTileView floatingTile2 = new PlayerTileView(context);
		floatingTile2.setId(View.generateViewId());
		GridLayout.LayoutParams floating2Lp = new GridLayout.LayoutParams();
		floating2Lp.width = dpToPx(context, 350);
		floating2Lp.height = dpToPx(context, 170);
		floating2Lp.columnSpec = GridLayout.spec(2);
		floating2Lp.rowSpec = GridLayout.spec(1);
		floating2Lp.setGravity(android.view.Gravity.END | android.view.Gravity.BOTTOM);
		floating2Lp.setMargins(8, 4, 8, 8);
		floatingTile2.setLayoutParams(floating2Lp);
		final int floating2Idx = 2;
		floatingTile2.setOnLongClickListener(v -> { if (listener != null) listener.onTileLongClick(floating2Idx); return true; });
		tiles.add(floatingTile2);
		grid.addView(floatingTile2);
	}

	private static int dpToPx(Context context, int dp) {
		float density = context.getResources().getDisplayMetrics().density;
		return Math.round(dp * density);
	}

	public interface TileLongClickListener {
		void onTileLongClick(int index);
	}
}


