package cc.nobug.linkeye;

import android.os.Handler;
import android.os.Looper;
import android.widget.Toast;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 分配控制器：负责初始/顺序分配、设置摄像头/频道以及保存
 */
public final class TileAssignmentController {

	private TileAssignmentController() { }

	/** 单个分配条目 */
	public static final class Assignment {
		public final int tileIndex;
		public final CameraInfo camera;
		public final boolean muted;
		public Assignment(int tileIndex, CameraInfo camera, boolean muted) {
			this.tileIndex = tileIndex;
			this.camera = camera;
			this.muted = muted;
		}
	}

	/** 顺序分配，避免同时初始化造成拥塞 */
	public static void assignSequentially(List<PlayerTileView> tiles, List<Assignment> assignments) {
		assignSequentiallyInternal(tiles, assignments, 0);
	}

	private static void assignSequentiallyInternal(List<PlayerTileView> tiles, List<Assignment> assignments, int index) {
		if (assignments == null || tiles == null) return;
		if (index >= assignments.size()) return;
		Assignment a = assignments.get(index);
		if (a.tileIndex < tiles.size()) {
			PlayerTileView tile = tiles.get(a.tileIndex);
			tile.assignCamera(a.camera);
			tile.setMuted(a.muted);
		}
		int delay = (index == 0) ? 1000 : 300;
		new Handler(Looper.getMainLooper()).postDelayed(() -> assignSequentiallyInternal(tiles, assignments, index + 1), delay);
	}

	/** 设置摄像头并默认静音(非TV)；保持唯一性，清理其他格同ID */
	public static void setTileCamera(List<PlayerTileView> tiles, List<CameraInfo> cameras, int tileIndex, int cameraIndex, SaveCallback saveCallback) {
		if (tileIndex < 0 || tileIndex >= tiles.size()) return;
		CameraInfo assign = null;
		if (cameraIndex >= 0 && cameraIndex < cameras.size()) {
			assign = cameras.get(cameraIndex);
			for (int i = 0; i < tiles.size(); i++) {
				if (i == tileIndex) continue;
				CameraInfo other = tiles.get(i).getAssignedCamera();
				if (other != null && assign.id.equals(other.id)) {
					tiles.get(i).assignCamera(null);
				}
			}
		}
		tiles.get(tileIndex).assignCamera(assign);
		boolean defaultMuted = (assign == null || !assign.isTvChannel());
		tiles.get(tileIndex).setMuted(defaultMuted);
		if (saveCallback != null) saveCallback.save(tileIndex, assign != null ? assign.id : "", defaultMuted);
		tiles.get(tileIndex).post(() -> tiles.get(tileIndex).requestFocus());
	}

	/** 设置电视频道：不静音；保持唯一性，清理其他格同频道 */
	public static void setTileTvChannel(List<PlayerTileView> tiles, int tileIndex, TvChannel tvChannel, SaveCallback saveCallback) {
		if (tileIndex < 0 || tileIndex >= tiles.size()) return;
		tvChannel.resetSourceIndex();
		for (int i = 0; i < tiles.size(); i++) {
			if (i == tileIndex) continue;
			CameraInfo other = tiles.get(i).getAssignedCamera();
			if (other != null && tvChannel.id.equals(other.id)) {
				tiles.get(i).assignCamera(null);
			}
		}
		tiles.get(tileIndex).assignCamera(tvChannel);
		tiles.get(tileIndex).setMuted(false);
		if (saveCallback != null) saveCallback.save(tileIndex, "channel_" + tvChannel.id, false);
		tiles.get(tileIndex).post(() -> tiles.get(tileIndex).requestFocus());
	}

	/** 保存回调：由调用者决定如何持久化 */
	public interface SaveCallback {
		void save(int tileIndex, String cameraId, boolean muted);
	}
}


