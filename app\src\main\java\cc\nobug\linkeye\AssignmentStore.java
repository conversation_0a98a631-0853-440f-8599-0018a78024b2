package cc.nobug.linkeye;

import android.content.Context;
import android.content.SharedPreferences;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 分配存储：负责按布局保存各窗口的摄像头/频道分配与静音状态
 * 将 JSON 存取封装为简洁接口
 */
public final class AssignmentStore {

	private static final String KEY_CAMERA_ASSIGNMENTS = "camera_assignments_json";

	private final SharedPreferences sharedPreferences;

	public AssignmentStore(Context context) {
		this.sharedPreferences = context.getSharedPreferences(AppStateStore.PREFS, Context.MODE_PRIVATE);
	}

	/** 读取全部分配 JSON */
	public JSONObject loadAll() {
		String jsonString = sharedPreferences.getString(KEY_CAMERA_ASSIGNMENTS, "{}");
		try {
			return new JSONObject(jsonString);
		} catch (JSONException e) {
			return new JSONObject();
		}
	}

	/** 保存全部分配 JSON */
	public void saveAll(JSONObject root) {
		SharedPreferences.Editor ed = sharedPreferences.edit();
		ed.putString(KEY_CAMERA_ASSIGNMENTS, root.toString());
		ed.apply();
	}

	/** 获取指定布局的分配对象（若不存在则返回空对象） */
	public JSONObject getLayoutAssignments(JSONObject root, String layoutKey) {
		try {
			if (root.has(layoutKey)) {
				return root.getJSONObject(layoutKey);
			}
		} catch (JSONException ignored) { }
		return new JSONObject();
	}

	/**
	 * 保存某个视图的摄像头/频道 ID 与静音状态
	 * layoutKey: 当前布局键
	 * viewIndex: 视图索引（view0/view1/...）
	 */
	public void saveSelection(String layoutKey, int viewIndex, String cameraId, boolean muted) {
		JSONObject all = loadAll();
		JSONObject layout = getLayoutAssignments(all, layoutKey);
		JSONObject assignments = layout.optJSONObject("assignments");
		JSONObject mutedStates = layout.optJSONObject("muted");
		if (assignments == null) assignments = new JSONObject();
		if (mutedStates == null) mutedStates = new JSONObject();
		try {
			assignments.put("view" + viewIndex, cameraId);
			mutedStates.put("view" + viewIndex, muted);
			layout.put("assignments", assignments);
			layout.put("muted", mutedStates);
			all.put(layoutKey, layout);
			saveAll(all);
		} catch (JSONException ignored) { }
	}
}


