package cc.nobug.linkeye;

import android.app.Activity;
import android.app.AlertDialog;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.KeyEvent;
import android.view.View;
import android.widget.GridLayout;
import android.widget.Toast;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import tv.danmaku.ijk.media.player.IjkMediaPlayer;

public class MainActivity extends Activity implements TileMenuController.GridLayoutProvider {

    private static final int GRID_MODE_TV = -1; // Special value for TV mode

    private GridLayout grid;
    private final List<PlayerTileView> tiles = new ArrayList<>();

    // 抽离后的状态/分配存储
    private AppStateStore appStateStore;
    private AssignmentStore assignmentStore;

    private List<CameraInfo> cameras = new ArrayList<>();
    private final List<TvGroup> tvGroups = new ArrayList<>();
    private int gridMode = 4; // 1,2,4 or GRID_MODE_TV
    private int page = 0; // for 4-grid
    private boolean isTvMode = false;
    
    // Channel switching cooldown to prevent rapid key presses
    private long lastChannelSwitchTime = 0;
    private static final long CHANNEL_SWITCH_COOLDOWN_MS = 500; // 500ms cooldown

    // 菜单控制器
    private TileMenuController tileMenuController;

    // 按键控制器
    private KeyEventController keyEventController;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Enable fullscreen mode and hide system UI
        enableFullscreen();

        setContentView(R.layout.activity_main);

        // 初始化状态与分配存储
        appStateStore = new AppStateStore(this);
        assignmentStore = new AssignmentStore(this);

        // Load IJK libraries once
        try {
            IjkMediaPlayer.loadLibrariesOnce(null);
            IjkMediaPlayer.native_profileBegin("libijkplayer.so");
        } catch (UnsatisfiedLinkError e) {
            Toast.makeText(this, "Failed to load ijkplayer libs" + e.getMessage(), Toast.LENGTH_LONG).show();
        }

        grid = (GridLayout) findViewById(R.id.grid_container);

        cameras = CameraRepository.load(this);
        restoreState();
        buildGrid();

        // 初始化菜单控制器（持有列表引用，动态更新生效）
        tileMenuController = new TileMenuController(
            this,
            tiles,
            cameras,
            tvGroups,
            this::saveCameraSelection,
            () -> isTvMode
        );

        // 初始化按键控制器
        keyEventController = new KeyEventController(new KeyEventController.Callbacks() {
            @Override public boolean isTvMode() { return isTvMode; }
            @Override public int getGridMode() { return gridMode; }
            @Override public List<PlayerTileView> getTiles() { return tiles; }
            @Override public void chooseGridMode() { MainActivity.this.chooseGridMode(); }
            @Override public void showTileMenu(int tileIndex) { MainActivity.this.showTileMenu(tileIndex); }
            @Override public void pageNext() { MainActivity.this.pageNext(); }
            @Override public void pagePrev() { MainActivity.this.pagePrev(); }
            @Override public void switchToNextChannel(int tileIndex) { MainActivity.this.switchToNextChannel(tileIndex); }
            @Override public void switchToPreviousChannel(int tileIndex) { MainActivity.this.switchToPreviousChannel(tileIndex); }
        });

        // 加载电视频道（异步）
        loadTvChannels();
    }

    private void loadTvChannels() {
        TvChannelManager.loadChannels(this, new TvChannelManager.ChannelsCallback() {
            @Override
            public void onSuccess(List<TvGroup> groups) {
                tvGroups.clear();
                tvGroups.addAll(groups);
                if (isTvMode && !tiles.isEmpty()) {
                    runOnUiThread(() -> assignInitialTvChannels());
                }
            }

            @Override
            public void onError(String error) {
                runOnUiThread(() -> Toast.makeText(MainActivity.this, "加载电视频道失败: " + error, Toast.LENGTH_LONG).show());
            }
        });
    }

    private void showM3U8UrlDialog() { tileMenuController.showM3U8UrlDialog(); }

    // 测试连接逻辑已在 TileMenuController 中实现

    private void buildGrid() {
        grid.removeAllViews();
        tiles.clear();

        if (isTvMode) {
            TileGridController.buildTvModeGrid(this, grid, tiles, this::showTileMenu);
        } else {
            TileGridController.buildNormalGrid(this, grid, tiles, gridMode, this::showTileMenu);
        }

        // 设置各 tile 的焦点导航
        setupFocusNavigation();

        // Request focus on the first tile after a short delay to ensure layout is complete
        if (!tiles.isEmpty()) {
            tiles.get(0).post(() -> tiles.get(0).requestFocus());
        }
    }

    // Helper methods for JSON-based storage
    private String getCurrentLayoutKey() {
        if (isTvMode) {
            return "layout_tv_mode";
        } else {
            return "layout_" + gridMode + "_grid";
        }
    }

    private CameraInfo findCameraById(String cameraId) {
        if (cameraId == null || cameraId.isEmpty()) return null;
        if (cameraId.startsWith("channel_")) {
            for (TvGroup group : tvGroups) {
                for (TvChannel channel : group.channels) {
                    if (cameraId.replace("channel_", "").equals(channel.id)) {
                        return channel;
                    }
                }
            }
        } else {
            for (CameraInfo camera : cameras) {
                if (cameraId.equals(camera.id)) {
                    return camera;
                }
            }
        }
        return null;
    }

    // Load camera assignments from JSON storage
    private JSONObject loadCameraAssignments() {
        // 使用 AssignmentStore 统一管理分配 JSON
        return assignmentStore.loadAll();
    }

    // Get assignments for current layout
    private JSONObject getCurrentLayoutAssignments(JSONObject allAssignments) {
        String layoutKey = getCurrentLayoutKey();
        return assignmentStore.getLayoutAssignments(allAssignments, layoutKey);
    }

    // Get camera assignment for a specific tile
    private String getCameraAssignment(JSONObject layoutAssignments, int tileIndex) {
        try {
            JSONObject assignments = layoutAssignments.optJSONObject("assignments");
            if (assignments != null) {
                return assignments.optString("view" + tileIndex, "");
            }
        } catch (Exception e) {
            // Fall through to return empty
        }
        return "";
    }

    // Get mute state for a specific tile
    private boolean getMuteState(JSONObject layoutAssignments, int tileIndex, boolean defaultMuted) {
        try {
            JSONObject muted = layoutAssignments.optJSONObject("muted");
            if (muted != null && muted.has("view" + tileIndex)) {
                return muted.getBoolean("view" + tileIndex);
            }
        } catch (JSONException e) {
            // Fall through to return default
        }
        return defaultMuted;
    }

    private void setupFocusNavigation() {
        if (tiles.isEmpty()) return;

        if (isTvMode) {
            setupTvModeFocusNavigation();
        } else {
            setupNormalFocusNavigation();
        }
    }

    private void setupNormalFocusNavigation() {
        if (gridMode == 3) {
            FocusNavigator.setupThreeWay(tiles);
            return;
        }
        FocusNavigator.setupNormal(tiles, gridMode);
    }

    private void setupTvModeFocusNavigation() {
        // 委托到 FocusNavigator
        FocusNavigator.setupTvMode(tiles);
    }

    private void assignInitialCameras() {
        // Only restore saved assignments from JSON - no default assignments
        JSONObject allAssignments = loadCameraAssignments();
        JSONObject layoutAssignments = getCurrentLayoutAssignments(allAssignments);

        // Prepare camera assignments from saved data only
        List<CameraAssignment> assignments = new ArrayList<>();
        int tileCount = (gridMode == 3) ? 3 : gridMode;
        for (int i = 0; i < tileCount; i++) {
            String cameraId = getCameraAssignment(layoutAssignments, i);
            CameraInfo assigned = findCameraById(cameraId);
            // Default to muted for cameras, unmuted for TV channels
            boolean defaultMuted = (assigned == null || !assigned.isTvChannel());
            boolean muted = getMuteState(layoutAssignments, i, defaultMuted);
            assignments.add(new CameraAssignment(i, assigned, muted));
        }

        // 顺序分配，避免同时初始化
        List<TileAssignmentController.Assignment> list = new ArrayList<>();
        for (CameraAssignment a : assignments) list.add(new TileAssignmentController.Assignment(a.tileIndex, a.camera, a.muted));
        TileAssignmentController.assignSequentially(tiles, list);
    }

    private void assignInitialTvChannels() {
        if (!isTvMode || tiles.size() != 3) return;

        // In TV mode: only restore saved assignments from JSON - no default assignments
        JSONObject allAssignments = loadCameraAssignments();
        JSONObject layoutAssignments = getCurrentLayoutAssignments(allAssignments);

        // Prepare camera assignments from saved data only
        List<CameraAssignment> assignments = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            String cameraId = getCameraAssignment(layoutAssignments, i);
            CameraInfo assigned = findCameraById(cameraId);
            // Main tile (index 0) defaults to unmuted, floating tiles default to muted unless it's a TV channel
            boolean defaultMuted = (i != 0) && (assigned == null || !assigned.isTvChannel());
            boolean muted = getMuteState(layoutAssignments, i, defaultMuted);
            assignments.add(new CameraAssignment(i, assigned, muted));
        }

        // 顺序分配，避免同时初始化
        List<TileAssignmentController.Assignment> list = new ArrayList<>();
        for (CameraAssignment a : assignments) list.add(new TileAssignmentController.Assignment(a.tileIndex, a.camera, a.muted));
        TileAssignmentController.assignSequentially(tiles, list);
    }

    private static class CameraAssignment {
        final int tileIndex;
        final CameraInfo camera;
        final boolean muted;

        CameraAssignment(int tileIndex, CameraInfo camera, boolean muted) {
            this.tileIndex = tileIndex;
            this.camera = camera;
            this.muted = muted;
        }
    }

    // 顺序分配逻辑已迁移至 TileAssignmentController

    private void showTileMenu(int tileIndex) {
        if (isTvMode && tileIndex == 0) tileMenuController.showTvChannelPicker(tileIndex);
        else tileMenuController.showTileMenu(tileIndex);
    }

    private void setTileTvChannel(int tileIndex, TvChannel tvChannel) {
        if (tileIndex < 0 || tileIndex >= tiles.size()) return;

        // Reset the channel's source index
        tvChannel.resetSourceIndex();

        // Check if this TV channel is already used in current layout and clear it
        for (int i = 0; i < tiles.size(); i++) {
            if (i == tileIndex) continue;
            CameraInfo other = tiles.get(i).getAssignedCamera();
            if (other != null && tvChannel.id.equals(other.id)) {
                tiles.get(i).assignCamera(null);
            }
        }

        // Assign the TV channel to the tile
        TileAssignmentController.setTileTvChannel(tiles, tileIndex, tvChannel, this::saveCameraSelection);
    }

    private void switchToNextChannel(int tileIndex) {
        if (!isTvMode || tileIndex < 0 || tileIndex >= tiles.size() || tvGroups.isEmpty()) return;

        // Check cooldown to prevent rapid channel switching
        long currentTime = System.currentTimeMillis();
        if (ChannelSwitcher.isWithinCooldown(lastChannelSwitchTime, currentTime, CHANNEL_SWITCH_COOLDOWN_MS)) {
            return; // Ignore rapid key presses
        }
        lastChannelSwitchTime = currentTime;

        PlayerTileView tile = tiles.get(tileIndex);
        CameraInfo currentCamera = tile.getAssignedCamera();

        // Find current channel and switch to next
        TvChannel nextChannel = ChannelSwitcher.findNext(tvGroups, currentCamera);
        if (nextChannel != null) {
            setTileTvChannel(tileIndex, nextChannel);
            // Show a brief toast with channel name
            Toast.makeText(this, "频道: " + nextChannel.name, Toast.LENGTH_SHORT).show();
        }
    }

    private void switchToPreviousChannel(int tileIndex) {
        if (!isTvMode || tileIndex < 0 || tileIndex >= tiles.size() || tvGroups.isEmpty()) return;

        // Check cooldown to prevent rapid channel switching
        long currentTime = System.currentTimeMillis();
        if (ChannelSwitcher.isWithinCooldown(lastChannelSwitchTime, currentTime, CHANNEL_SWITCH_COOLDOWN_MS)) {
            return; // Ignore rapid key presses
        }
        lastChannelSwitchTime = currentTime;

        PlayerTileView tile = tiles.get(tileIndex);
        CameraInfo currentCamera = tile.getAssignedCamera();

        // Find current channel and switch to previous
        TvChannel previousChannel = ChannelSwitcher.findPrevious(tvGroups, currentCamera);
        if (previousChannel != null) {
            setTileTvChannel(tileIndex, previousChannel);
            // Show a brief toast with channel name
            Toast.makeText(this, "频道: " + previousChannel.name, Toast.LENGTH_SHORT).show();
        }
    }

    // 频道查找逻辑已迁移至 ChannelSwitcher

    // Save camera selection for a specific tile - only saves camera ID and mute state
    private void saveCameraSelection(int tileIndex, String cameraId, boolean muted) {
        // 通过 AssignmentStore 以布局键进行保存
        String layoutKey = getCurrentLayoutKey();
        assignmentStore.saveSelection(layoutKey, tileIndex, cameraId, muted);
    }

    // Save basic app state (grid mode, TV mode, page)
    private void saveBasicState() {
        // 通过 AppStateStore 统一保存
        appStateStore.saveBasicState(gridMode, isTvMode, page);
    }

    private void restoreState() {
        // 通过 AppStateStore 读取
        gridMode = appStateStore.getGridMode();
        isTvMode = appStateStore.isTvMode();

        if (isTvMode) {
            gridMode = GRID_MODE_TV;
        } else if (gridMode != 1 && gridMode != 2 && gridMode != 3 && gridMode != 4) {
            gridMode = 4;
        }

        page = appStateStore.getPage();
    }

    private void chooseGridMode() {
        String[] items = new String[]{"1路", "2路", "3路", "4路", "电视模式", "设置M3U8地址"};
        new AlertDialog.Builder(this)
                .setTitle("设置")
                .setItems(items, (dialog, which) -> {
                    if (which == 5) {
                        // Set M3U8 URL
                        showM3U8UrlDialog();
                        return;
                    }

                    boolean newTvMode = (which == 4);
                    int newMode;

                    if (newTvMode) {
                        newMode = GRID_MODE_TV;
                    } else {
                        newMode = (which == 0 ? 1 : which == 1 ? 2 : which == 2 ? 3 : 4);
                    }

                    if (newMode != gridMode || newTvMode != isTvMode) {
                        isTvMode = newTvMode;
                        gridMode = newMode;
                        page = 0;
                        saveBasicState();
                        buildGrid();
                        if (isTvMode) {
                            assignInitialTvChannels();
                        } else {
                            assignInitialCameras();
                        }
                    }
                })
                .show();
    }

    private void pageNext() {
        if (cameras.isEmpty()) return;
        if (gridMode == 4) {
            int pages = (cameras.size() + 3) / 4;
            if (pages <= 1) return;
            page = (page + 1) % pages;
            saveBasicState();
            assignInitialCameras();
        }
    }

    private void pagePrev() {
        if (cameras.isEmpty()) return;
        if (gridMode == 4) {
            int pages = (cameras.size() + 3) / 4;
            if (pages <= 1) return;
            page = (page - 1 + pages) % pages;
            saveBasicState();
            assignInitialCameras();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        View focused = getCurrentFocus();
        boolean handled = keyEventController.onKeyDown(keyCode, focused);
        if (handled) return true;
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onStop() {
        super.onStop();
        for (PlayerTileView t : tiles) t.release();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        IjkMediaPlayer.native_profileEnd();
    }

    private void enableFullscreen() {
        // 使用 FullscreenController 统一管理
        FullscreenController.enableFullscreen(this);
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        // 交由 FullscreenController 处理
        FullscreenController.onWindowFocusChanged(this, hasFocus);
    }

    // GridLayoutProvider 实现，用于菜单控制器延迟回调
    @Override
    public GridLayout getGridLayout() { return grid; }
}
