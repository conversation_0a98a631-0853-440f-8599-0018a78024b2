package cc.nobug.linkeye;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.ImageView;
import android.view.View;
import android.view.KeyEvent;
import android.os.Handler;
import android.os.Looper;

import tv.danmaku.ijk.media.player.IMediaPlayer;
import tv.danmaku.ijk.media.player.IjkMediaPlayer;

import java.io.IOException;

public class PlayerTileView extends FrameLayout implements SurfaceHolder.Callback {
    private SurfaceView surfaceView;
    private SurfaceHolder surfaceHolder;
    private TextView titleView;
    private ImageView muteBadge;
    private View focusBorder;
    private View loadingIndicator;

    private IjkMediaPlayer player;
    private CameraInfo camera;
    private boolean muted;

    private Handler focusHandler = new Handler(Looper.getMainLooper());
    
    // Flag to prevent concurrent camera assignments
    private volatile boolean isAssigning = false;

    // Retry mechanism for failed connections
    private int retryCount = 0;
    private static final int MAX_RETRY_COUNT = 3;
    private static final int RETRY_DELAY_MS = 2000; // 2 seconds

    // TV channel source switching
    private boolean isRetryingTvSources = false;

    public PlayerTileView(Context context) {
        super(context);
        init(context);
    }

    public PlayerTileView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public PlayerTileView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.player_tile, this, true);
        surfaceView = (SurfaceView) findViewById(R.id.surface_view);
        surfaceHolder = surfaceView.getHolder();
        surfaceHolder.addCallback(this);
        titleView = (TextView) findViewById(R.id.title);
        muteBadge = (ImageView) findViewById(R.id.mute_badge);
        focusBorder = findViewById(R.id.focus_border);
        loadingIndicator = findViewById(R.id.loading_indicator);

        setFocusable(true);
        setFocusableInTouchMode(false); // Important for TV navigation
        setClickable(true);

        // Disable default focus highlighting to prevent system overlay
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            setDefaultFocusHighlightEnabled(false);
        }

        // Ensure no foreground overlay
//        setForeground(null);

        setOnFocusChangeListener((v, hasFocus) -> {
            if (focusBorder != null) {
                if (hasFocus) {
                    // Use the enhanced focus drawable with cyan border and outer glow
                    focusBorder.setBackgroundResource(R.drawable.focus_border_enhanced);
                } else {
                    focusBorder.setBackground(null);
                }
            }
        });
    }

    public void assignCamera(CameraInfo info) {
        // Prevent concurrent camera assignments
        if (isAssigning) {
            return; // Ignore concurrent calls
        }
        
        isAssigning = true;
        
        try {
            this.camera = info;
            this.retryCount = 0; // Reset retry count when assigning new camera
            this.isRetryingTvSources = false; // Reset TV source retry flag

            // Reset TV channel source index if it's a TV channel
            if (info instanceof TvChannel) {
                ((TvChannel) info).resetSourceIndex();
            }

            updateTitle();
            if (this.camera == null) {
                release();
                // Set surface to black when no camera assigned
                if (surfaceView != null) {
                    surfaceView.setBackgroundColor(0xFF000000); // Black background
                }
                isAssigning = false; // Reset flag
                return;
            }
            // Clear black background when camera is assigned
            if (surfaceView != null) {
                surfaceView.setBackgroundColor(0x00000000); // Transparent background
            }
            
            // Release current player and wait a bit before creating new one
            release();
            
            // Delay the restart to ensure proper cleanup, then reset flag
            focusHandler.postDelayed(() -> {
                try {
                    if (camera != null) {
                        restartIfReady();
                    }
                } finally {
                    isAssigning = false; // Always reset flag
                }
            }, 200); // 200ms delay for proper cleanup
            
        } catch (Exception e) {
            isAssigning = false; // Reset flag on error
        }
    }

    public CameraInfo getAssignedCamera() {
        return camera;
    }

    public void setMuted(boolean muted) {
        this.muted = muted;
        applyMute();
    }

    public boolean isMuted() {
        return muted;
    }

    private void updateTitle() {
        if (titleView != null) {
            String title = "空";
            if (camera != null) {
                title = camera.name;
                // Show source info for TV channels with multiple sources
                if (camera instanceof TvChannel) {
                    TvChannel tvChannel = (TvChannel) camera;
                    if (tvChannel.getSourceCount() > 1) {
                        title += " (" + (tvChannel.getCurrentSourceIndex() + 1) + "/" + tvChannel.getSourceCount() + ")";
                    }
                }
            }
            titleView.setText(title);
        }
        if (muteBadge != null) {
            muteBadge.setVisibility(muted ? VISIBLE : GONE);
        }
    }

    private void applyMute() {
        // Use local reference to avoid race condition with async release
        IjkMediaPlayer currentPlayer = player;
        if (currentPlayer != null) {
            try {
                float vol = muted ? 0f : 1f;
                currentPlayer.setVolume(vol, vol);
            } catch (Exception e) {
                // Ignore errors if player is in invalid state
            }
        }
        if (muteBadge != null) {
            muteBadge.setVisibility(muted ? VISIBLE : GONE);
        }
    }

    private void restartIfReady() {
        if (surfaceHolder != null && surfaceHolder.getSurface().isValid()) {
            prepareAndStart();
        }
    }

    private void prepareAndStart() {
        release();
        if (camera == null) {
            hideLoadingIndicator();
            return;
        }

        String url = getCurrentUrl();
        if (url == null || url.isEmpty()) {
            hideLoadingIndicator();
            return;
        }

        // Show loading indicator when starting to connect
        showLoadingIndicator();

        // Small delay to ensure previous player is fully released before creating new one
        focusHandler.postDelayed(() -> {
            // Double check camera is still assigned after delay
            if (camera == null) {
                hideLoadingIndicator();
                return;
            }
            
            createAndConfigurePlayer();
        }, 100); // 100ms delay
    }

    private void createAndConfigurePlayer() {
        String url = getCurrentUrl();
        if (url == null || url.isEmpty()) {
            hideLoadingIndicator();
            return;
        }
        
        player = new IjkMediaPlayer();
        // Add more robust options for better connection stability
        if (camera instanceof TvChannel) {
            player.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "rtsp_transport", "udp");
            player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec", 1);
            player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec-all-videos", 1);
            player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec-hevc", 1);
            player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec-handle-resolution-change", 1);
        } else {
            player.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "rtsp_transport", "tcp");
            player.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "rtsp_flags", "prefer_tcp");
            player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec", 0);
            player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec-all-videos", 0);
            player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec-hevc", 0);
            player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec-handle-resolution-change", 0);
        }
        // Audio configuration to ensure audio playback works
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "enable-accurate-seek", 0);
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, "skip_loop_filter", 48);
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, "skip_frame", 0);
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "framedrop", 1);
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max_cached_duration", 3000);
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "infbuf", 1);
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "fflags", "fastseek");

        // Audio specific options to fix audio playback issues
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "opensles", 0);
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "soundtouch", 1);
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "analyzeduration", 1);
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "probesize", 1024 * 16);
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "flush_packets", 1);

        player.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "timeout", 10000000); // 10 seconds timeout
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", 1);
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "reconnect", 1);
        final int SDL_FCC_YV12 = 0x32315659;
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "overlay-format", SDL_FCC_YV12);
        player.setDisplay(surfaceHolder);
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "packet-buffering", 0);


        try {
            player.setDataSource(url);
            player.setOnPreparedListener(new IjkMediaPlayer.OnPreparedListener() {
                @Override
                public void onPrepared(IMediaPlayer mp) {
                    retryCount = 0; // Reset retry count on successful preparation
                    isRetryingTvSources = false; // Reset TV source retry flag
                    hideLoadingIndicator(); // Hide loading indicator on success
                    updateTitle(); // Update title to show current source info
                    applyMute();
                    mp.start();
                }
            });
            player.setOnErrorListener(new IjkMediaPlayer.OnErrorListener() {
                @Override
                public boolean onError(IMediaPlayer mp, int what, int extra) {
                    return handlePlaybackError();
                }
            });
            player.prepareAsync();
        } catch (IOException e) {
            handlePlaybackError();
        }
    }

    private String getCurrentUrl() {
        if (camera instanceof TvChannel) {
            return ((TvChannel) camera).getCurrentSource();
        } else {
            return camera.url;
        }
    }

    private boolean handlePlaybackError() {
        // First try different sources for TV channels
        if (camera instanceof TvChannel && !isRetryingTvSources) {
            TvChannel tvChannel = (TvChannel) camera;
            if (tvChannel.hasNextSource()) {
                isRetryingTvSources = true;
                tvChannel.getNextSource(); // Switch to next source
                focusHandler.postDelayed(() -> {
                    if (camera != null) {
                        prepareAndStart();
                    }
                }, 1000); // Shorter delay for source switching
                return true;
            }
        }

        // If no more sources or not a TV channel, try regular retry
        if (retryCount < MAX_RETRY_COUNT) {
            retryCount++;
            focusHandler.postDelayed(() -> {
                if (camera != null) {
                    prepareAndStart();
                }
            }, RETRY_DELAY_MS);
        } else {
            // All retries exhausted, reset for next attempt
            retryCount = 0;
            isRetryingTvSources = false;
            if (camera instanceof TvChannel) {
                ((TvChannel) camera).resetSourceIndex();
            }
        }
        return true; // Always handle the error
    }


    private void showLoadingIndicator() {
        if (loadingIndicator != null) {
            loadingIndicator.setVisibility(VISIBLE);
        }
    }

    private void hideLoadingIndicator() {
        if (loadingIndicator != null) {
            loadingIndicator.setVisibility(GONE);
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        // Handle specific keys if needed
        switch (keyCode) {
            case KeyEvent.KEYCODE_DPAD_CENTER:
            case KeyEvent.KEYCODE_ENTER:
            case KeyEvent.KEYCODE_BUTTON_A:
                // Let parent handle this (MainActivity will show menu)
                return super.onKeyDown(keyCode, event);
            case KeyEvent.KEYCODE_DPAD_LEFT:
            case KeyEvent.KEYCODE_DPAD_RIGHT:
            case KeyEvent.KEYCODE_DPAD_UP:
            case KeyEvent.KEYCODE_DPAD_DOWN:
                // Let the system handle directional navigation
                return super.onKeyDown(keyCode, event);
            default:
                return super.onKeyDown(keyCode, event);
        }
    }

    public void release() {
        hideLoadingIndicator(); // Hide loading indicator when releasing
        
        if (player != null) {
            // Store reference and immediately set player to null to prevent further usage
            final IjkMediaPlayer playerToRelease = player;
            player = null;
            
            // Perform player release operations in background thread to avoid UI blocking
            new Thread(() -> {
                try {
                    if (playerToRelease != null) {
                        try { 
                            playerToRelease.stop(); 
                        } catch (Exception e) {
                            // Ignore stop errors
                        }
                        try { 
                            playerToRelease.reset(); 
                        } catch (Exception e) {
                            // Ignore reset errors
                        }
                        try { 
                            playerToRelease.release(); 
                        } catch (Exception e) {
                            // Ignore release errors
                        }
                    }
                } catch (Exception e) {
                    // Catch any unexpected exceptions to prevent crashes
                }
            }).start();
        }
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        restartIfReady();
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) { }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        release();
    }
}
